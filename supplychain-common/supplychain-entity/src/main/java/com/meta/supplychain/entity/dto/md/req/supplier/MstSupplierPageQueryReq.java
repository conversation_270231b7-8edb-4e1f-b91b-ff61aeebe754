package com.meta.supplychain.entity.dto.md.req.supplier;

import cn.linkkids.framework.croods.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商分页查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商分页查询请求")
public class MstSupplierPageQueryReq extends PageParams {

    @Schema(description = "供应商编码")
    private String code;

    @Schema(description = "供应商名称")
    private String name;

    @Schema(description = "关键字（模糊查询供应商编码和名称）")
    private String keyword;

    @Schema(description = "启用状态 1启用；0停用")
    private Integer status;

    @Schema(description = "经营方式 J经销；D代销；L联营；Z租赁")
    private String operateMode;

    @Schema(description = "结算模式 1统一结算；2本地结算；3区域结算")
    private Integer settleMode;

    @Schema(description = "经营控制 1不控制；2暂停进货；3清场")
    private Integer operateControl;

    @Schema(description = "付款控制 1允许付款；2不允许付款；3不允许结算")
    private Integer paymentControl;

    @Schema(description = "结算方法 1账期结算；2预付款；3其他；4现采现结")
    private Integer settlementMethod;
}
