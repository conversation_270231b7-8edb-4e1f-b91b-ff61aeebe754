package com.meta.supplychain.convert.md;

import com.meta.supplychain.entity.dto.md.supplier.MstSupplierDTO;
import com.meta.supplychain.entity.po.md.MstSupplierPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 供应商转换器
 */
@Mapper
public interface MstSupplierConvert {

    MstSupplierConvert INSTANCE = Mappers.getMapper(MstSupplierConvert.class);

    /**
     * PO转DTO
     */
    MstSupplierDTO po2Dto(MstSupplierPO po);

    /**
     * PO列表转DTO列表
     */
    List<MstSupplierDTO> po2DtoList(List<MstSupplierPO> poList);

    /**
     * DTO转PO
     */
    MstSupplierPO dto2Po(MstSupplierDTO dto);
}
