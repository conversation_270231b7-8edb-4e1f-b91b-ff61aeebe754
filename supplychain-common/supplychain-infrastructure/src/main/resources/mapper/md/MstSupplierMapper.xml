<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierMapper">

    <!-- 分页查询供应商 -->
    <select id="pageQuerySupplier" resultType="com.meta.supplychain.entity.po.md.MstSupplierPO">
        SELECT
            id,
            type,
            code,
            name,
            short_name,
            link_man,
            phone,
            fax,
            email,
            status,
            down_time,
            supp_cate_code,
            supp_cate_name,
            supp_type,
            operate_mode,
            payment_control,
            operate_control,
            settle_mode,
            invoice_type,
            supp_tax_rate,
            province_code,
            province_name,
            city_code,
            city_name,
            district_code,
            district_name,
            address,
            tax_number,
            ibps_code,
            bank_name,
            bank_code,
            bank_account_name,
            bank_account,
            del_flag,
            tenant_id,
            out_code,
            source,
            settlement_method,
            direct_order_push_time,
            order_ctrl,
            merge_dc,
            esign,
            einv,
            supp_conf,
            ent_type,
            supp_bussines_type,
            legal_person_name,
            legal_cert_type,
            legal_cert_number,
            legal_phone,
            registered_capital,
            enterprise_name,
            business_scope,
            merchant_category,
            remark,
            credit_code,
            invoice_category,
            invoice_address,
            recipient_address,
            recipient_name,
            recipient_phone,
            postal_code,
            suspend_business_time,
            clearance_time,
            invoice_phone,
            category_code,
            category_name,
            category_item_code,
            invoice_bank_name,
            invoice_bank_account,
            create_time,
            create_code,
            create_name,
            create_uid,
            update_time,
            update_code,
            update_name,
            update_uid
        FROM mst_supplier
        <where>
            del_flag = 0
            <if test="request.code != null and request.code != ''">
                AND code = #{request.code}
            </if>
            <if test="request.name != null and request.name != ''">
                AND name = #{request.name}
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                AND (code LIKE CONCAT('%', #{request.keyword}, '%')
                     OR name LIKE CONCAT('%', #{request.keyword}, '%'))
            </if>
            <if test="request.status != null">
                AND status = #{request.status}
            </if>
            <if test="request.operateMode != null and request.operateMode != ''">
                AND operate_mode LIKE CONCAT('%', #{request.operateMode}, '%')
            </if>
            <if test="request.settleMode != null">
                AND settle_mode = #{request.settleMode}
            </if>
            <if test="request.operateControl != null">
                AND operate_control = #{request.operateControl}
            </if>
            <if test="request.paymentControl != null">
                AND payment_control = #{request.paymentControl}
            </if>
            <if test="request.settlementMethod != null">
                AND settlement_method = #{request.settlementMethod}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
