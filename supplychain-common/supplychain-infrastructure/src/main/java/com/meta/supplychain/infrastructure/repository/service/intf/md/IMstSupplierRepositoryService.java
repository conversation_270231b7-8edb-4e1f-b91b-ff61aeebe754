package com.meta.supplychain.infrastructure.repository.service.intf.md;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierPageQueryReq;
import com.meta.supplychain.entity.po.md.MstSupplierPO;

public interface IMstSupplierRepositoryService extends IService<MstSupplierPO> {

    /**
     * 分页查询供应商
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<MstSupplierPO> pageQuerySupplier(Page<MstSupplierPO> page, MstSupplierPageQueryReq request);
}
