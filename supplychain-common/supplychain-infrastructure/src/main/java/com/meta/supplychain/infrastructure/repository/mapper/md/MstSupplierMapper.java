package com.meta.supplychain.infrastructure.repository.mapper.md;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierPageQueryReq;
import com.meta.supplychain.entity.po.md.MstSupplierPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 供应商Mapper接口
 */
@Mapper
public interface MstSupplierMapper extends BaseMapper<MstSupplierPO> {

    /**
     * 分页查询供应商
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<MstSupplierPO> pageQuerySupplier(Page<MstSupplierPO> page, MstSupplierPageQueryReq request);
}
