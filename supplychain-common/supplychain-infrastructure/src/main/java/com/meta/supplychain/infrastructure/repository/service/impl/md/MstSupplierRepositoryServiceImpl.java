package com.meta.supplychain.infrastructure.repository.service.impl.md;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierPageQueryReq;
import com.meta.supplychain.entity.dto.md.supplier.MstSupplierDTO;
import com.meta.supplychain.entity.po.md.MstSupplierPO;
import com.meta.supplychain.infrastructure.repository.mapper.md.MstSupplierMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierRepositoryService;
import org.springframework.stereotype.Service;

/**
 * 供应商RepositoryService实现类
 */
@Service
public class MstSupplierRepositoryServiceImpl extends ServiceImpl<MstSupplierMapper, MstSupplierPO> implements IMstSupplierRepositoryService {

    @Override
    public Page<MstSupplierPO> pageQuerySupplier(Page<MstSupplierPO> page, MstSupplierPageQueryReq request) {
        return baseMapper.pageQuerySupplier(page, request);
    }

    @Override
    public boolean updateSupplierByCode(MstSupplierDTO supplierDTO) {
        return lambdaUpdate()
                .eq(MstSupplierPO::getCode, supplierDTO.getCode())
                .set(MstSupplierPO::getType, supplierDTO.getType())
                .set(MstSupplierPO::getName, supplierDTO.getName())
                .set(MstSupplierPO::getShortName, supplierDTO.getShortName())
                .set(MstSupplierPO::getLinkMan, supplierDTO.getLinkMan())
                .set(MstSupplierPO::getPhone, supplierDTO.getPhone())
                .set(MstSupplierPO::getFax, supplierDTO.getFax())
                .set(MstSupplierPO::getEmail, supplierDTO.getEmail())
                .set(MstSupplierPO::getStatus, supplierDTO.getStatus())
                .set(MstSupplierPO::getDownTime, supplierDTO.getDownTime())
                .set(MstSupplierPO::getSuppCateCode, supplierDTO.getSuppCateCode())
                .set(MstSupplierPO::getSuppCateName, supplierDTO.getSuppCateName())
                .set(MstSupplierPO::getSuppType, supplierDTO.getSuppType())
                .set(MstSupplierPO::getOperateMode, supplierDTO.getOperateMode())
                .set(MstSupplierPO::getPaymentControl, supplierDTO.getPaymentControl())
                .set(MstSupplierPO::getOperateControl, supplierDTO.getOperateControl())
                .set(MstSupplierPO::getSettleMode, supplierDTO.getSettleMode())
                .set(MstSupplierPO::getInvoiceType, supplierDTO.getInvoiceType())
                .set(MstSupplierPO::getSuppTaxRate, supplierDTO.getSuppTaxRate())
                .set(MstSupplierPO::getProvinceCode, supplierDTO.getProvinceCode())
                .set(MstSupplierPO::getProvinceName, supplierDTO.getProvinceName())
                .set(MstSupplierPO::getCityCode, supplierDTO.getCityCode())
                .set(MstSupplierPO::getCityName, supplierDTO.getCityName())
                .set(MstSupplierPO::getDistrictCode, supplierDTO.getDistrictCode())
                .set(MstSupplierPO::getDistrictName, supplierDTO.getDistrictName())
                .set(MstSupplierPO::getAddress, supplierDTO.getAddress())
                .set(MstSupplierPO::getTaxNumber, supplierDTO.getTaxNumber())
                .set(MstSupplierPO::getIbpsCode, supplierDTO.getIbpsCode())
                .set(MstSupplierPO::getBankName, supplierDTO.getBankName())
                .set(MstSupplierPO::getBankCode, supplierDTO.getBankCode())
                .set(MstSupplierPO::getBankAccountName, supplierDTO.getBankAccountName())
                .set(MstSupplierPO::getBankAccount, supplierDTO.getBankAccount())
                .set(MstSupplierPO::getOutCode, supplierDTO.getOutCode())
                .set(MstSupplierPO::getSource, supplierDTO.getSource())
                .set(MstSupplierPO::getSettlementMethod, supplierDTO.getSettlementMethod())
                .set(MstSupplierPO::getDirectOrderPushTime, supplierDTO.getDirectOrderPushTime())
                .set(MstSupplierPO::getOrderCtrl, supplierDTO.getOrderCtrl())
                .set(MstSupplierPO::getMergeDc, supplierDTO.getMergeDc())
                .set(MstSupplierPO::getEsign, supplierDTO.getEsign())
                .set(MstSupplierPO::getEinv, supplierDTO.getEinv())
                .set(MstSupplierPO::getSuppConf, supplierDTO.getSuppConf())
                .set(MstSupplierPO::getEntType, supplierDTO.getEntType())
                .set(MstSupplierPO::getSuppBussinesType, supplierDTO.getSuppBussinesType())
                .set(MstSupplierPO::getLegalPersonName, supplierDTO.getLegalPersonName())
                .set(MstSupplierPO::getLegalCertType, supplierDTO.getLegalCertType())
                .set(MstSupplierPO::getLegalCertNumber, supplierDTO.getLegalCertNumber())
                .set(MstSupplierPO::getLegalPhone, supplierDTO.getLegalPhone())
                .set(MstSupplierPO::getRegisteredCapital, supplierDTO.getRegisteredCapital())
                .set(MstSupplierPO::getEnterpriseName, supplierDTO.getEnterpriseName())
                .set(MstSupplierPO::getBusinessScope, supplierDTO.getBusinessScope())
                .set(MstSupplierPO::getMerchantCategory, supplierDTO.getMerchantCategory())
                .set(MstSupplierPO::getRemark, supplierDTO.getRemark())
                .set(MstSupplierPO::getCreditCode, supplierDTO.getCreditCode())
                .set(MstSupplierPO::getInvoiceCategory, supplierDTO.getInvoiceCategory())
                .set(MstSupplierPO::getInvoiceAddress, supplierDTO.getInvoiceAddress())
                .set(MstSupplierPO::getRecipientAddress, supplierDTO.getRecipientAddress())
                .set(MstSupplierPO::getRecipientName, supplierDTO.getRecipientName())
                .set(MstSupplierPO::getRecipientPhone, supplierDTO.getRecipientPhone())
                .set(MstSupplierPO::getPostalCode, supplierDTO.getPostalCode())
                .set(MstSupplierPO::getSuspendBusinessTime, supplierDTO.getSuspendBusinessTime())
                .set(MstSupplierPO::getClearanceTime, supplierDTO.getClearanceTime())
                .set(MstSupplierPO::getInvoicePhone, supplierDTO.getInvoicePhone())
                .set(MstSupplierPO::getCategoryCode, supplierDTO.getCategoryCode())
                .set(MstSupplierPO::getCategoryName, supplierDTO.getCategoryName())
                .set(MstSupplierPO::getCategoryItemCode, supplierDTO.getCategoryItemCode())
                .set(MstSupplierPO::getInvoiceBankName, supplierDTO.getInvoiceBankName())
                .set(MstSupplierPO::getInvoiceBankAccount, supplierDTO.getInvoiceBankAccount())
                .update();
    }
}
