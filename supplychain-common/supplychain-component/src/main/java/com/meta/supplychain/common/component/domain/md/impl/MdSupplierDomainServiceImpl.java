package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.intf.IMdSupplierDomainService;
import com.meta.supplychain.convert.md.MstSupplierConvert;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierPageQueryReq;
import com.meta.supplychain.entity.dto.md.supplier.MstSupplierDTO;
import com.meta.supplychain.entity.po.md.MstSupplierPO;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierCategoryRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierRepositoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商相关业务接口
 */
@Service
public class MdSupplierDomainServiceImpl implements IMdSupplierDomainService {

    @Resource
    private IMstSupplierRepositoryService supplierRepositoryService;

    @Resource
    private IMstSupplierCategoryRepositoryService categoryRepositoryService;

    @Override
    public PageResult<MstSupplierDTO> pageQuerySupplier(MstSupplierPageQueryReq request) {
        Page<MstSupplierPO> pageResult = supplierRepositoryService.pageQuerySupplier(
                new Page<>(request.getCurrent(), request.getPageSize()), request);

        List<MstSupplierDTO> result = pageResult.getRecords().stream()
                .map(MstSupplierConvert.INSTANCE::po2Dto)
                .collect(Collectors.toList());

        return PageResult.of(pageResult.getTotal(), result);
    }
}
