package com.meta.supplychain.common.component.domain.md.impl;

import com.meta.supplychain.common.component.domain.md.intf.IMdSupplierDomainService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierCategoryRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMstSupplierRepositoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 供应商相关业务接口
 */
@Service
public class MdSupplierDomainServiceImpl implements IMdSupplierDomainService {

    @Resource
    private IMstSupplierRepositoryService supplierRepositoryService;

    @Resource
    private IMstSupplierCategoryRepositoryService categoryRepositoryService;

    
}
