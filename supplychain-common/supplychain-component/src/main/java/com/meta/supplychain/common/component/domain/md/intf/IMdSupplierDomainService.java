package com.meta.supplychain.common.component.domain.md.intf;

import cn.linkkids.framework.croods.common.PageResult;
import com.meta.supplychain.entity.dto.md.req.supplier.MstSupplierPageQueryReq;
import com.meta.supplychain.entity.dto.md.supplier.MstSupplierDTO;

public interface IMdSupplierDomainService {

    /**
     * 分页查询供应商
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    PageResult<MstSupplierDTO> pageQuerySupplier(MstSupplierPageQueryReq request);
}
